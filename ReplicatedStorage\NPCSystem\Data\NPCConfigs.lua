-- NPCConfigs.lua (ModuleScript)
-- Configuration data for different NPC types

local NPCConfigs = {
    -- Gunslinger NPCs (3 variants)
    gunslinger = {
        name = "Gunslinger",
        type = "gunslinger",
        cost = 0, -- Free for testing
        description = "A quick-draw expert with deadly accuracy and lightning-fast reflexes.",
        variants = {"GunSlinger0", "GunSlinger1", "GunSlinger2"}, -- Template names in workspace

        -- Stats
        maxHealth = 120,
        walkSpeed = 16,
        runSpeed = 22,
        damage = 35,
        armor = 8,
        accuracy = 90,
        range = 45,

        -- Abilities
        abilities = {"quick_draw", "dual_wield", "ricochet_shot", "fan_the_hammer"},

        -- Appearance
        appearance = {
            bodyColors = {
                head = "Nougat",
                torso = "Brown",
                leftArm = "Nougat",
                rightArm = "Nougat",
                leftLeg = "Brown",
                rightLeg = "Brown"
            },
            accessories = {"Cowboy Hat", "Dual Revolvers", "Holsters", "Spurs"}
        },

        -- AI Behavior
        behavior = {
            aggressive = true,
            followDistance = 10,
            attackRange = 35,
            guardRadius = 18,
            preferredRange = 25
        }
    },

    -- Rustler NPC
    rustler = {
        name = "Rustler",
        type = "rustler",
        cost = 0, -- Free for testing
        description = "A cunning outlaw skilled in stealth, theft, and guerrilla tactics.",

        -- Stats
        maxHealth = 95,
        walkSpeed = 20,
        runSpeed = 28,
        damage = 25,
        armor = 5,
        stealth = 85,
        agility = 80,

        -- Abilities
        abilities = {"sneak_attack", "lasso", "smoke_bomb", "horse_call"},

        -- Appearance
        appearance = {
            bodyColors = {
                head = "Nougat",
                torso = "Really black",
                leftArm = "Nougat",
                rightArm = "Nougat",
                leftLeg = "Really black",
                rightLeg = "Really black"
            },
            accessories = {"Bandana", "Lasso", "Knife", "Saddlebags"}
        },

        -- AI Behavior
        behavior = {
            aggressive = false,
            followDistance = 12,
            attackRange = 15,
            guardRadius = 20,
            stealthRole = true,
            priority = "flanking"
        }
    },

    -- Ironclad NPC
    ironclad = {
        name = "Ironclad",
        type = "ironclad",
        cost = 0, -- Free for testing
        description = "A heavily armored frontier defender with unbreakable resolve and massive firepower.",

        -- Stats
        maxHealth = 200,
        walkSpeed = 12,
        runSpeed = 16,
        damage = 40,
        armor = 20,
        resistance = 30,

        -- Abilities
        abilities = {"iron_wall", "heavy_shot", "armor_charge", "intimidate"},

        -- Appearance
        appearance = {
            bodyColors = {
                head = "Dark stone grey",
                torso = "Dark stone grey",
                leftArm = "Dark stone grey",
                rightArm = "Dark stone grey",
                leftLeg = "Dark stone grey",
                rightLeg = "Dark stone grey"
            },
            accessories = {"Iron Helmet", "Heavy Rifle", "Armor Plating", "Steel Boots"}
        },

        -- AI Behavior
        behavior = {
            aggressive = true,
            followDistance = 8,
            attackRange = 12,
            guardRadius = 15,
            tankRole = true,
            priority = "defense"
        }
    }
}

-- Utility functions for NPC configs
function NPCConfigs.getConfig(npcType)
    return NPCConfigs[npcType]
end

function NPCConfigs.getAllTypes()
    local types = {}
    for npcType, _ in pairs(NPCConfigs) do
        if type(NPCConfigs[npcType]) == "table" and NPCConfigs[npcType].name then
            table.insert(types, npcType)
        end
    end
    return types
end

function NPCConfigs.getCost(npcType)
    local config = NPCConfigs[npcType]
    return config and config.cost or 0
end

function NPCConfigs.validateConfig(npcType)
    local config = NPCConfigs[npcType]
    if not config then return false, "NPC type not found" end
    
    -- Check required fields
    local required = {"name", "type", "cost", "maxHealth", "walkSpeed"}
    for _, field in ipairs(required) do
        if not config[field] then
            return false, "Missing required field: " .. field
        end
    end
    
    return true, "Valid configuration"
end

return NPCConfigs
