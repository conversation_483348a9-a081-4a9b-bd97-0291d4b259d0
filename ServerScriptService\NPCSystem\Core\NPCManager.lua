-- NPCManager.lua (<PERSON>ript)
-- Manages all NPCs in the game

local NPCManager = {}

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

-- Import dependencies
local BaseNPC = require(script.Parent.BaseNPC)
local CommandHandler = require(script.Parent.CommandHandler)
local NPCConfigs = require(ReplicatedStorage.NPCSystem.Data.NPCConfigs)

-- Remote events with error handling
local NPCRemotes = ReplicatedStorage:WaitForChild("NPCRemotes", 10)
if not NPCRemotes then
    error("NPCRemotes folder not found in ReplicatedStorage!")
end

local PurchaseNPC = NPCRemotes:WaitForChild("PurchaseNPC", 10)
local SendCommand = NPCRemotes:WaitForChild("SendCommand", 10)
local NPCStatusUpdate = NPCRemotes:WaitForChild("NPCStatusUpdate", 10)
local GetNPCList = NPCRemotes:WaitF<PERSON><PERSON>hild("GetNPCList", 10)

if not PurchaseNPC then error("PurchaseNP<PERSON> remote not found!") end
if not SendCommand then error("SendCommand remote not found!") end
if not NPCStatusUpdate then error("NPCStatusUpdate remote not found!") end
if not GetNPCList then error("GetNPCList remote not found!") end

print("✓ All remote events loaded successfully")

-- Storage
NPCManager.activeNPCs = {}
NPCManager.playerNPCs = {}
NPCManager.playerCurrency = {} -- Session-based currency

-- Initialize the manager
function NPCManager:init()
    print("NPCManager initializing...")

    -- Connect remote events
    print("Connecting PurchaseNPC event...")
    PurchaseNPC.OnServerEvent:Connect(function(player, npcType)
        print("🔥 SERVER: PurchaseNPC event received!")
        print("Player:", player.Name)
        print("NPC Type:", npcType)

        local success, error = pcall(function()
            self:handleNPCPurchase(player, npcType)
        end)

        if not success then
            warn("Error in handleNPCPurchase:", error)
            NPCStatusUpdate:FireClient(player, "purchase_failed", {reason = "Server error: " .. tostring(error)})
        end
    end)
    print("✓ PurchaseNPC event connected")
    
    SendCommand.OnServerEvent:Connect(function(player, npcId, command, ...)
        self:handleCommand(player, npcId, command, ...)
    end)
    
    GetNPCList.OnServerInvoke = function(player)
        return self:getPlayerNPCs(player)
    end
    
    -- Start update loop
    RunService.Heartbeat:Connect(function(deltaTime)
        self:update(deltaTime)
    end)

    -- Handle player leaving
    Players.PlayerRemoving:Connect(function(player)
        if self.playerNPCs[player] then
            for _, npc in ipairs(self.playerNPCs[player]) do
                self:removeNPC(npc.id)
            end
            self.playerNPCs[player] = nil
        end

        self.playerCurrency[player] = nil
    end)

    print("NPCManager initialized successfully")
end

function NPCManager:handleNPCPurchase(player, npcType)
    print("🔥 SERVER: handleNPCPurchase called")
    print("Player:", player.Name)
    print("NPC Type:", npcType)

    -- Check if NPC type exists
    local config = NPCConfigs[npcType]
    if not config then
        warn("Invalid NPC type:", npcType)
        NPCStatusUpdate:FireClient(player, "purchase_failed", {reason = "Invalid NPC type: " .. npcType})
        return
    end

    print("✓ NPC config found:", config.name)

    -- Initialize player currency if not exists (starting currency)
    if not self.playerCurrency[player] then
        self.playerCurrency[player] = 1000 -- Starting currency
    end

    local playerCurrency = self.playerCurrency[player]
    if playerCurrency < config.cost then
        NPCStatusUpdate:FireClient(player, "purchase_failed", {reason = "Not enough currency"})
        return
    end

    -- Create the NPC
    local npc = self:createNPC(npcType, config, player)
    if npc then
        -- Deduct currency
        self.playerCurrency[player] = self.playerCurrency[player] - config.cost

        -- Add to player's NPCs
        if not self.playerNPCs[player] then
            self.playerNPCs[player] = {}
        end
        table.insert(self.playerNPCs[player], npc)

        -- Notify client
        NPCStatusUpdate:FireClient(player, "purchased", {
            npcId = npc.id,
            npcType = npcType,
            name = npc.name
        })
    else
        NPCStatusUpdate:FireClient(player, "purchase_failed", {reason = "Failed to create NPC"})
    end
end

function NPCManager:createNPC(npcType, config, owner)
    -- Create NPC model
    local npcModel = self:createNPCModel(npcType, config)
    if not npcModel then
        warn("Failed to create NPC model for type:", npcType)
        return nil
    end

    -- Create NPC instance
    local npc = BaseNPC.new(npcModel, config)
    if not npc then
        warn("Failed to create BaseNPC instance")
        npcModel:Destroy()
        return nil
    end

    -- Set owner
    npc.owner = owner

    -- Add to active NPCs
    self.activeNPCs[npc.id] = npc

    return npc
end

function NPCManager:createNPCModel(npcType, config)
    local model = Instance.new("Model")
    model.Name = config.name or npcType

    -- Create basic humanoid structure
    local humanoid = Instance.new("Humanoid")
    humanoid.Parent = model

    -- Add basic parts
    local head = Instance.new("Part")
    head.Name = "Head"
    head.Size = Vector3.new(2, 1, 1)
    head.BrickColor = BrickColor.new("Bright yellow")
    head.Parent = model

    local torso = Instance.new("Part")
    torso.Name = "Torso"
    torso.Size = Vector3.new(2, 2, 1)
    torso.BrickColor = BrickColor.new("Bright blue")
    torso.Parent = model

    local humanoidRootPart = Instance.new("Part")
    humanoidRootPart.Name = "HumanoidRootPart"
    humanoidRootPart.Size = Vector3.new(2, 2, 1)
    humanoidRootPart.BrickColor = BrickColor.new("Bright blue")
    humanoidRootPart.Transparency = 1
    humanoidRootPart.CanCollide = false
    humanoidRootPart.Parent = model

    model.PrimaryPart = humanoidRootPart
    model.Parent = workspace
    model:MoveTo(Vector3.new(0, 5, 0))

    return model
end

function NPCManager:handleCommand(player, npcId, command, ...)
    local npc = self.activeNPCs[npcId]
    if not npc then
        warn("NPC not found:", npcId)
        return
    end
    
    -- Check if player owns this NPC
    if npc.owner ~= player then
        warn("Player", player.Name, "doesn't own NPC", npcId)
        return
    end
    
    -- Execute command through command handler
    local success = CommandHandler:executeCommand(npc, command, ...)
    
    if success then
        NPCStatusUpdate:FireClient(player, "command_executed", {
            npcId = npcId,
            command = command
        })
    end
end

function NPCManager:getPlayerNPCs(player)
    local npcs = self.playerNPCs[player] or {}
    local npcList = {}

    for _, npc in ipairs(npcs) do
        if npc.isActive then
            table.insert(npcList, {
                id = npc.id,
                name = npc.name,
                type = npc.config.type or "unknown"
            })
        end
    end

    return npcList
end

function NPCManager:addPlayerCurrency(player, amount)
    if not self.playerCurrency[player] then
        self.playerCurrency[player] = 1000 -- Starting currency
    end

    self.playerCurrency[player] = self.playerCurrency[player] + amount
    return self.playerCurrency[player]
end

function NPCManager:getPlayerCurrency(player)
    if not self.playerCurrency[player] then
        self.playerCurrency[player] = 1000 -- Starting currency
    end

    return self.playerCurrency[player]
end

function NPCManager:update(deltaTime)
    for _, npc in pairs(self.activeNPCs) do
        if npc.isActive then
            npc:update(deltaTime)
        end
    end
end

function NPCManager:removeNPC(npcId)
    local npc = self.activeNPCs[npcId]
    if npc then
        npc:destroy()
        self.activeNPCs[npcId] = nil
        
        -- Remove from player's NPC list
        if npc.owner and self.playerNPCs[npc.owner] then
            for i, playerNPC in ipairs(self.playerNPCs[npc.owner]) do
                if playerNPC.id == npcId then
                    table.remove(self.playerNPCs[npc.owner], i)
                    break
                end
            end
        end
    end
end

return NPCManager
